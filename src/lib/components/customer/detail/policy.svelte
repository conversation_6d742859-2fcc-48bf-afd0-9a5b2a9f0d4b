<script lang="ts">
    import { t } from '$lib/stores/i18n';
    import { 
        TicketSolid, 
        ClipboardListSolid, 
        CalendarMonthSolid, 
        ClockSolid, 
        CloseCircleSolid,
    } from 'flowbite-svelte-icons';

    export let customer_policies: any;

    // Policy status order for sorting
    const statusOrder = ['active', 'pending', 'nearly_expired', 'expired', 'cancelled', 'inactive'];
    
    // Display 3 policies initially, and toggle the rest
    let showMore = false;
    $: visiblePolicies = showMore
    ? customer_policies.policies
    : customer_policies.policies.slice(0, customer_policies.length);
    
    // Sort policies by status
    $: sortedVisiblePolicies = customer_policies.policies.sort((a: any, b: any) => {
        const aIndex = statusOrder.indexOf(a.policy_status.toLowerCase());
        const bIndex = statusOrder.indexOf(b.policy_status.toLowerCase());
        return aIndex - bIndex;
    });

    // Policy type icons mapping
    type PolicyTypeKey = 'compulsory_motor' | 'car' | 'home' | 'cancer' | 'health_accident_travel' | 
                         'cyber' | 'business' | 'general' | 'basic' | 'premium' | 'executive';
    const typeIcons: Record<PolicyTypeKey, string> & Record<string, string> = {
        'compulsory_motor': '🚗',
        'car': '🚙',
        'home': '🏠',
        'cancer': '❤️',
        'health_accident_travel': '🏥',
        'cyber': '⚡︎',
        'business': '🏢',
        'general': '📄',
        'basic': '📋',
        'premium': '⭐',
        'executive': '💼',
    };

    // Status color classes for badges
    type StatusKey = 'active' | 'inactive' | 'nearly_expired' | 'expired' | 'pending' | 'cancelled';
    const statusColors: Record<StatusKey, string> & Record<string, string> = {
        'active': 'bg-green-100 text-green-500 border-green-200',
        'pending': 'bg-blue-100 text-blue-500 border-blue-200',
        'nearly_expired': 'bg-yellow-100 text-yellow-500 border-yellow-200',
        'expired': 'bg-red-100 text-red-500 border-red-200',
        'inactive': 'bg-gray-100 text-gray-500 border-gray-200',
        'cancelled': 'bg-gray-100 text-gray-500 border-gray-200',
    };

    // Function to format date
    const displayDateDraft = (timestamp: string) => {
        const displayCreated = new Date(timestamp);
        
        // Format each part separately
        const day = displayCreated.getDate().toString().padStart(2, '0');
        const month = displayCreated.toLocaleString('en-US', { month: 'short' });
        const year = displayCreated.getFullYear();
        
        // Add hour and minute in 24-hour format
        const hour = displayCreated.getHours().toString().padStart(2, '0');
        const minute = displayCreated.getMinutes().toString().padStart(2, '0');
        
        // Combine in desired format
        return `${day} ${month} ${year}`;
        // return `${day} ${month} ${year} ${hour}:${minute}`;
    };
</script>

<!-- <p class="text-sm text-gray-500 dark:text-gray-400">
<b>Ticket:</b>
</p> -->
<!-- Policy Statistics-->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
    <!-- Total Policies -->
    <div class="bg-gray-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-gray-200 mr-3">
            <TicketSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-gray-500 text-sm">{t('total_policies')}</p>
            <p class="text-2xl font-bold">{customer_policies.statistics.total_policies}</p>
        </div>
    </div>

    <!-- Active Policies -->
    <div class="bg-green-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-green-200 mr-3">
            <ClipboardListSolid class="h-6 w-6 text-gray-700" />
        </div>
        <div>
            <p class="text-green-500 text-sm">{t('active')}</p>
            <p class="text-green-500 text-2xl font-bold">{customer_policies.statistics.active_policies}</p>
        </div>
    </div>

    <!-- Waiting Period Policies -->
    <div class="bg-blue-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-blue-200 mr-3">
            <CalendarMonthSolid class="h-6 w-6 text-blue-700" />
        </div>
        <div>
            <p class="text-blue-500 text-sm">{t('pending')}</p>
            <p class="text-blue-500 text-2xl font-bold">{customer_policies.statistics.waiting_period_policies}</p>
        </div>
    </div>

    <!-- Nearly Expired Policies -->
    <div class="bg-yellow-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-yellow-200 mr-3">
            <ClockSolid class="h-6 w-6 text-yellow-700" />
        </div>
        <div>
            <p class="text-yellow-500 text-sm">{t('nearly_expired')}</p>
            <p class="text-yellow-500 text-2xl font-bold">{customer_policies.statistics.nearly_expired_policies}</p>
        </div>
    </div>

    <!-- Expired Policies -->
    <div class="bg-red-100 rounded-lg p-4 flex items-center">
        <div class="p-2 rounded-full bg-red-200 mr-3">
            <CloseCircleSolid class="h-6 w-6 text-red-700" />
        </div>
        <div>
            <p class="text-red-500 text-sm">{t('expired')}</p>
            <p class="text-red-500 text-2xl font-bold">{customer_policies.statistics.expired_policies}</p>
        </div>
    </div>
</div>

<section
    class="grid gap-6 py-6
           grid-cols-1
           sm:grid-cols-1
           md:grid-cols-2
           lg:grid-cols-2
           xl:grid-cols-3
           2xl:grid-cols-3"
    aria-label="Policy cards grid"
>
    {#each sortedVisiblePolicies as policy, index}
    <button
        class="bg-white rounded-lg shadow-md hover:shadow-lg
               transition-all duration-300 ease-in-out
               transform hover:scale-105
               border border-gray-100
               min-h-[480px] p-6
               flex flex-col justify-between
               cursor-pointer text-left w-full"
        aria-labelledby="title"
    >
        <!-- Policy Header -->
        <div class="mb-4">
            <div class="flex items-center justify-between mb-3">
                <h2 
                    id="policy-title"
                    class="text-lg font-semibold text-gray-900 flex items-center gap-2"
                >
                    <span class="text-2xl" aria-hidden="true">{typeIcons[policy.product?.product_type?.toLowerCase()] || typeIcons.general}</span>
                </h2>
                <div class="flex flex-row items-end gap-1">
                    <span 
                        class="px-3 py-1 rounded-full text-xs font-medium border
                            {statusColors[policy.policy_status?.toLowerCase()] || statusColors.inactive}"
                        aria-label="Member status: {policy.policy_status}"
                    >
                        <!-- {policy.policy_status?.toUpperCase().replace(/_/g, ' ')} -->
                        {t(policy.policy_status?.toLowerCase())}
                    </span>
                    <span
                        class="px-2 py-1 rounded-full text-xs font-medium bg-violet-100 
                            text-violet-500 border border-violet-200"
                    >
                        VIP
                    </span>
                </div>
            </div>

            <div class="space-y-1 mb-3">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <!-- {policy.product?.product_type || 'N/A'} -->
                     {policy.product?.name || 'N/A'}
                </h2>
            </div>

            <!-- Policy Numbers -->
            <div class="space-y-1 mb-3">
                <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                >
                    <span class="text-sm text-gray-500">{t('policy_no')}</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                        ?
                    </span>
                </p>
                <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                >
                    <span class="text-sm text-gray-500">{t('policy_certificate_no')}</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                        ?
                    </span>
                </p>
                <!-- {#if policy.InsurerCardNo} -->
                <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                >
                    <span class="text-sm text-gray-500">{t('policy_insurance_card_no')}</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                        ?
                    </span>
                </p>
                <!-- {/if} -->
                <!-- {#if policy.StaffNo} -->
                <p
                    class="text-sm text-gray-600 flex justify-between items-center"
                >
                    <span class="text-sm text-gray-500">{t('policy_agent_no')}</span>
                    <span class="text-sm font-medium text-gray-900 text-right">
                        ?
                    </span>
                </p>
                <!-- {/if} -->
            </div>

            <!-- Policy Details -->
            <div class="space-y-4 flex-grow">

                <!-- Member & Card Information -->
                <div class="bg-gray-50 rounded-lg p-3 space-y-2">
                    <h3
                        class="text-xs font-semibold text-gray-700 uppercase tracking-wide"
                    >
                        {t('policy_member_section')}
                    </h3>
                    <div class="grid grid-cols-2 gap-2 text-xs">
                        <div>
                            <span class="text-gray-500">{t('policy_member_type')}</span>
                            <div class="font-medium text-gray-900">
                                ?
                            </div>
                        </div>
                        <div class="ml-3">
                            <span class="text-gray-500">{t('policy_member_card_type')}</span>
                            <div class="font-medium text-gray-900">
                                ?
                            </div>
                        </div>
                    </div>
                </div>

            <!-- Coverage & Premium Information -->
            <!-- {#if policy.CoverageAmount || policy.Premium} -->
            <div class="bg-blue-50 rounded-lg p-3 space-y-2">
                <h3
                    class="text-xs font-semibold text-blue-700 uppercase tracking-wide"
                >
                    {t('policy_coverage_section')}
                </h3>
                <div class="space-y-2 text-xs">
                    <!-- {#if policy.CoverageAmount} -->
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600">{t('policy_coverage')}</span>
                        <span class="font-semibold text-blue-900">
                            {policy.product.coverage?.toLocaleString('th-TH', {
                                style: 'currency',
                                currency: 'THB'
                            }) || 'N/A'}
                        </span>
                    </div>
                    <!-- {/if} -->
                    <!-- {#if policy.Premium} -->
                    <div class="flex justify-between items-center">
                        <span class="text-blue-600">{t('policy_premium')}</span>
                        <span class="font-semibold text-blue-900">
                            {policy.product.premium?.toLocaleString('th-TH', {
                                style: 'currency',
                                currency: 'THB'
                            }) || 'N/A'}
                        </span>
                    </div>
                    <!-- {/if} -->
                </div>
            </div>
            <!-- {/if} -->

            <!-- Plan & Date Information -->
            <div class="bg-green-50 rounded-lg p-3 space-y-2">
                <h3
                    class="text-xs font-semibold text-green-700 uppercase tracking-wide"
                >
                    {t('policy_coverage_period')}
                </h3>
                <div class="space-y-2 text-xs">
                    <div class="flex justify-between items-center">
                        <span class="text-green-600">{t('start_date')}</span>
                        <span class="font-medium text-green-900">
                            {policy.start_date ? displayDateDraft(policy.start_date) : 'N/A'}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-green-600">{t('end_date')}</span>
                        <span class="font-medium text-green-900">
                            {policy.end_date ? displayDateDraft(policy.end_date) : 'N/A'}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Company Information -->
            <div class="space-y-1 border-t border-gray-100 pt-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-500">{t('policy_insurer_name')}</span>
                    <span class="text-sm bg-gray-100 text-gray-700 px-2 py-1 rounded">
                        ?
                    </span>
                </div>
            </div>

            <!-- Plan Description -->
            <div>
                <div class="space-y-1">
                    <p class="text-xs text-gray-600 flex justify-between line-clamp-2 ml-2">
                        {t('policy_plan_no')}
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            ?
                        </span>
                    </p>
                    <p class="text-xs text-gray-600 flex justify-between line-clamp-2 ml-2">
                        {t('policy_plan_code')}
                        <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded">
                            ?
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </button>
    {/each}
</section>


<!-- See More / See Less button -->
<!-- {#if customer_policies.policies.length > 3}
    <div class="text-center">
        <button
            on:click={() => (showMore = !showMore)}
            class="px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition"
        >
            {showMore ? t('see_less') : t('see_more')}
        </button>
    </div>
{/if} -->
